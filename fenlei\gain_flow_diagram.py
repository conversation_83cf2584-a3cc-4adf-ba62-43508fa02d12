# GAIN算法One-Hot处理分类变量的流程图
def create_flow_diagram():
    """
    创建GAIN算法处理分类变量的详细流程图
    """
    
    flow_diagram = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                    fenlei项目GAIN算法One-Hot处理分类变量流程图                    ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    
    ┌─────────────────┐
    │  原始数据集     │  例如: credit.csv (30000×23)
    │  (混合类型)     │  包含连续变量和分类变量
    └─────────┬───────┘
              │
              ▼
    ┌─────────────────┐
    │  data_loader()  │  • 读取CSV文件
    │  数据加载       │  • 随机引入缺失值 (miss_rate)
    └─────────┬───────┘  • 生成原始掩码 data_m
              │
              ▼
    ┌─────────────────┐
    │ gain()函数开始  │  rawgain.py 主函数
    └─────────┬───────┘
              │
              ▼
    ╔═════════════════════════════════════════════════════════════════════════════╗
    ║                           第一阶段：数据预处理                                ║
    ╚═════════════════════════════════════════════════════════════════════════════╝
              │
              ▼
    ┌─────────────────┐
    │ 变量类型识别    │  identify_variable_types()
    │                 │  • 统计每列唯一值数量
    │ 唯一值 < 20     │  • unique_values < 20 → 'cat'
    │    ↓ 'cat'      │  • unique_values ≥ 20 → 'con'
    │ 唯一值 ≥ 20     │  • 建立分类变量映射字典
    │    ↓ 'con'      │
    └─────────┬───────┘
              │ 输出: ['con','cat','cat',...] + categorical_info
              ▼
    ┌─────────────────┐
    │  One-Hot编码    │  encode_categorical_variables()
    │                 │
    │ 连续变量:       │  ┌─────────────────────────────────┐
    │  [x] → [x]      │  │ 分类变量编码示例:               │
    │                 │  │ 原始: [0, 1, 2, NaN, 1]        │
    │ 分类变量:       │  │ 类别: {0,1,2}                   │
    │  [0,1,2] →      │  │ One-Hot:                        │
    │  [[1,0,0],      │  │ [1,0,0] ← 类别0                 │
    │   [0,1,0],      │  │ [0,1,0] ← 类别1                 │
    │   [0,0,1]]      │  │ [0,0,1] ← 类别2                 │
    │                 │  │ [NaN,NaN,NaN] ← 缺失值          │
    └─────────┬───────┘  │ [0,1,0] ← 类别1                 │
              │          └─────────────────────────────────┘
              │ 输出: encoded_data (n×d_encoded)
              ▼
    ┌─────────────────┐
    │  混合数据归一化  │  normalization_mixed()
    │                 │  • 连续变量: MinMax归一化 [0,1]
    │ 连续: [0,1]     │  • 分类变量: 保持 {0,1,NaN}
    │ 分类: {0,1,NaN} │
    └─────────┬───────┘
              │
              ▼
    ┌─────────────────┐
    │  生成新掩码     │  data_m = 1 - np.isnan(encoded_data)
    │                 │  适应One-Hot编码后的维度
    └─────────┬───────┘
              │
              ▼
    ╔═════════════════════════════════════════════════════════════════════════════╗
    ║                           第二阶段：神经网络训练                              ║
    ╚═════════════════════════════════════════════════════════════════════════════╝
              │
              ▼
    ┌─────────────────┐
    │  网络架构适配   │  • 输入维度: encoded_dim (自动适应)
    │                 │  • 生成器: [encoded_dim×2, h_dim, encoded_dim]
    │ G: 生成器       │  • 判别器: [encoded_dim×2, h_dim, encoded_dim]
    │ D: 判别器       │
    └─────────┬───────┘
              │
              ▼
    ┌─────────────────┐
    │  混合损失函数   │  根据变量类型计算不同损失:
    │                 │
    │ 连续变量:       │  ┌─────────────────────────────────┐
    │ MSE损失         │  │ 损失函数详细:                   │
    │ (M×X-M×G)²      │  │                                 │
    │                 │  │ 连续变量 (MSE):                 │
    │ 分类变量:       │  │ L_con = Σ(M×X - M×G_sample)²    │
    │ 交叉熵损失      │  │                                 │
    │ -X×M×log(G)     │  │ 分类变量 (交叉熵):              │
    └─────────┬───────┘  │ L_cat = -Σ(X×M×log(G_sample))   │
              │          │                                 │
              │          │ 总损失:                         │
              ▼          │ L_mixed = (L_con + L_cat) / Σ(M) │
    ┌─────────────────┐  └─────────────────────────────────┘
    │  对抗训练       │  • G_loss = 对抗损失 + α×混合损失
    │                 │  • D_loss = 标准对抗损失
    │ G ←→ D          │  • 交替训练 iterations 次
    │ (iterations次)  │
    └─────────┬───────┘
              │
              ▼
    ╔═════════════════════════════════════════════════════════════════════════════╗
    ║                           第三阶段：结果解码                                  ║
    ╚═════════════════════════════════════════════════════════════════════════════╝
              │
              ▼
    ┌─────────────────┐
    │  生成插补结果   │  imputed_encoded_data = G_sample
    │ (One-Hot格式)   │  仍为编码后的格式
    └─────────┬───────┘
              │
              ▼
    ┌─────────────────┐
    │  反归一化       │  renormalization_mixed()
    │                 │  • 连续变量: 恢复到原始范围
    │ 连续: 原始范围  │  • 分类变量: 保持 {0,1}
    │ 分类: {0,1}     │
    └─────────┬───────┘
              │
              ▼
    ┌─────────────────┐
    │  One-Hot解码    │  decode_categorical_variables()
    │                 │
    │ 连续变量:       │  ┌─────────────────────────────────┐
    │  直接复制       │  │ 解码示例:                       │
    │                 │  │ One-Hot: [0.1, 0.8, 0.1]       │
    │ 分类变量:       │  │ argmax → 索引1                  │
    │  argmax → 原值  │  │ index_to_value[1] → 原始类别值  │
    └─────────┬───────┘  └─────────────────────────────────┘
              │
              ▼
    ┌─────────────────┐
    │  分类变量舍入   │  rounding_mixed()
    │                 │  确保分类变量为整数
    └─────────┬───────┘
              │
              ▼
    ┌─────────────────┐
    │  最终插补结果   │  与原始数据相同格式 (n×d)
    │ (原始格式)      │  连续变量 + 分类变量
    └─────────┬───────┘
              │
              ▼
    ┌─────────────────┐
    │  性能评估       │  rmse_loss()
    │  RMSE计算       │  只考虑原本缺失的位置
    └─────────────────┘
    
    ╔═════════════════════════════════════════════════════════════════════════════╗
    ║                              关键创新点                                     ║
    ╚═════════════════════════════════════════════════════════════════════════════╝
    
    🔄 自动化流程:
       • 自动识别变量类型 (无需手动指定)
       • 自动适应网络维度 (根据编码后维度)
       • 自动选择损失函数 (根据变量类型)
    
    🎯 数学严谨性:
       • 标准One-Hot编码 (np.eye方式)
       • 正确交叉熵损失 (-X×M×log(G))
       • 适当归一化策略 (连续[0,1], 分类{0,1})
    
    🛡️ 缺失值处理:
       • One-Hot中保持NaN (缺失值→NaN向量)
       • 掩码适应维度变化 (encoded_dim)
       • 解码时argmax处理 (概率→类别)
    
    ⚡ 工程化优势:
       • 单文件集成 (rawgain.py)
       • 接口兼容 (与原框架一致)
       • 路径处理 (自动找到数据文件)
    """
    
    return flow_diagram

if __name__ == "__main__":
    print(create_flow_diagram())
