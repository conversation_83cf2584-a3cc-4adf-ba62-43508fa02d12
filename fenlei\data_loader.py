# coding=utf-8
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

'''Data loader for UCI letter, spam and MNIST datasets.
'''

# Necessary packages
import numpy as np
from utils import binary_sampler
from keras.datasets import mnist
import os
import pandas as pd


def data_loader (data_name, miss_rate):
  '''Loads datasets and introduce missingness.
  
  Args:
    - data_name: letter, spam, or mnist
    - miss_rate: the probability of missing components
    
  Returns:
    data_x: original data
    miss_data_x: data with missing values
    data_m: indicator matrix for missing components
  '''
  
  # Load data
  if data_name in ['Twin','breast','spam','letter','credit']:
    file_name = 'data/'+data_name+'.csv'
    # 使用pandas读取CSV文件，处理缺失值
    df = pd.read_csv(file_name)
    # 将缺失值填充为0
    df = df.fillna(0)
    # 转换为numpy数组
    data_x = df.values.astype(float)
  elif data_name == 'mnist':
    (data_x, _), _ = mnist.load_data()
    data_x = np.reshape(np.asarray(data_x), [60000, 28*28]).astype(float)

  # Parameters
  no, dim = data_x.shape
  
  # Introduce missing data
  data_m = binary_sampler(1-miss_rate, no, dim)
  miss_data_x = data_x.copy()
  miss_data_x[data_m == 0] = np.nan

  # # 创建保存缺失数据的目录
  # save_dir = f'missing_data_{data_name}'
  # if not os.path.exists(save_dir):
  #   os.makedirs(save_dir)
  #
  # # 保存缺失数据到CSV
  # save_path = os.path.join(save_dir, f'missing_rate_{miss_rate:.1f}.csv')
  # pd.DataFrame(miss_data_x).to_csv(save_path, index=False)

  return data_x, miss_data_x, data_m
