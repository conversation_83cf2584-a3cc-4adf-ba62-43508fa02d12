# 对比fenlei项目和DL-vs-Stat_Impute-main项目的GAIN实现
import numpy as np
import pandas as pd

def test_onehot_encoding_comparison():
    """对比两个项目的One-Hot编码实现"""
    print("=== One-Hot编码实现对比测试 ===\n")
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 10
    
    # 混合数据：连续变量 + 分类变量
    cont_var = np.random.normal(0, 1, n_samples)
    cat_var = np.random.choice([0, 1, 2], n_samples)  # 3个类别
    
    test_data = np.column_stack([cont_var, cat_var])
    
    # 添加缺失值
    test_data[2, 0] = np.nan  # 连续变量缺失
    test_data[5, 1] = np.nan  # 分类变量缺失
    
    print("原始测试数据:")
    print("连续变量 | 分类变量")
    for i in range(n_samples):
        print(f"{test_data[i, 0]:8.3f} | {test_data[i, 1]:8.0f}" if not np.isnan(test_data[i, 1]) else f"{test_data[i, 0]:8.3f} | {'NaN':>8}")
    
    print(f"\n数据形状: {test_data.shape}")
    print(f"连续变量唯一值数量: {len(np.unique(test_data[~np.isnan(test_data[:, 0]), 0]))}")
    print(f"分类变量唯一值数量: {len(np.unique(test_data[~np.isnan(test_data[:, 1]), 1]))}")
    
    # === 测试fenlei项目的实现 ===
    print("\n=== fenlei项目实现 ===")
    
    # 1. 变量类型识别
    variable_types = []
    categorical_info = {}
    categorical_threshold = 20
    
    for i in range(test_data.shape[1]):
        non_missing_values = test_data[~np.isnan(test_data[:, i]), i]
        unique_values = np.unique(non_missing_values)
        
        if len(unique_values) < categorical_threshold:
            variable_types.append('cat')
            categorical_info[i] = {
                'unique_values': unique_values,
                'value_to_index': {val: idx for idx, val in enumerate(unique_values)},
                'index_to_value': {idx: val for idx, val in enumerate(unique_values)}
            }
        else:
            variable_types.append('con')
    
    print(f"识别的变量类型: {variable_types}")
    for i, vtype in enumerate(variable_types):
        if vtype == 'cat':
            print(f"  变量{i} (分类): 类别 = {categorical_info[i]['unique_values']}")
        else:
            print(f"  变量{i} (连续)")
    
    # 2. One-Hot编码
    encoded_columns = []
    column_locations = []
    encoding_info = []
    current_pos = 0
    
    for i in range(test_data.shape[1]):
        if variable_types[i] == 'con':
            # 连续变量: 保持不变
            encoded_columns.append(test_data[:, i:i+1])
            current_pos += 1
            column_locations.append(current_pos)
            encoding_info.append({'type': 'con'})
        else:
            # 分类变量: one-hot编码
            cat_info = categorical_info[i]
            unique_values = cat_info['unique_values']
            n_categories = len(unique_values)
            
            # 创建one-hot编码矩阵
            one_hot_matrix = np.full((n_samples, n_categories), np.nan)
            
            for row_idx in range(n_samples):
                if not np.isnan(test_data[row_idx, i]):
                    value = test_data[row_idx, i]
                    if value in cat_info['value_to_index']:
                        cat_idx = cat_info['value_to_index'][value]
                        one_hot_matrix[row_idx, :] = 0
                        one_hot_matrix[row_idx, cat_idx] = 1
            
            encoded_columns.append(one_hot_matrix)
            current_pos += n_categories
            column_locations.append(current_pos)
            encoding_info.append({
                'type': 'cat',
                'n_categories': n_categories,
                'categorical_info': cat_info
            })
    
    # 拼接所有编码列
    fenlei_encoded_data = np.concatenate(encoded_columns, axis=1)
    
    print(f"\nfenlei编码后数据形状: {fenlei_encoded_data.shape}")
    print(f"列位置: {column_locations}")
    print("编码后数据:")
    print(fenlei_encoded_data)
    
    # === 模拟DL-vs-Stat_Impute-main的实现 ===
    print("\n=== DL-vs-Stat_Impute-main风格实现 ===")
    
    # 模拟DL-vs-Stat项目的编码方式
    def return_encode_column_dlvs(column_data, unique_values):
        """模拟DL-vs-Stat项目的编码函数"""
        dictionary = {val: idx for idx, val in enumerate(unique_values)}
        column_encode = []
        
        for value in column_data:
            if np.isnan(value):
                column_encode.append([np.nan for _ in range(len(dictionary))])
            else:
                one_hot = [0] * len(dictionary)
                if value in dictionary:
                    one_hot[dictionary[value]] = 1
                column_encode.append(one_hot)
        
        return np.array(column_encode, dtype=np.float32)
    
    # 对分类变量进行编码
    dlvs_encoded_columns = []
    dlvs_column_locations = []
    current_pos = 0
    
    for i in range(test_data.shape[1]):
        if variable_types[i] == 'con':
            # 连续变量
            dlvs_encoded_columns.append(test_data[:, i:i+1])
            current_pos += 1
            dlvs_column_locations.append(current_pos)
        else:
            # 分类变量
            unique_values = categorical_info[i]['unique_values']
            encoded_col = return_encode_column_dlvs(test_data[:, i], unique_values)
            dlvs_encoded_columns.append(encoded_col)
            current_pos += len(unique_values)
            dlvs_column_locations.append(current_pos)
    
    dlvs_encoded_data = np.concatenate(dlvs_encoded_columns, axis=1)
    
    print(f"DL-vs-Stat编码后数据形状: {dlvs_encoded_data.shape}")
    print(f"列位置: {dlvs_column_locations}")
    print("编码后数据:")
    print(dlvs_encoded_data)
    
    # === 对比结果 ===
    print("\n=== 对比结果 ===")
    
    # 检查形状是否一致
    shape_match = fenlei_encoded_data.shape == dlvs_encoded_data.shape
    print(f"数据形状一致: {shape_match}")
    
    # 检查数值是否一致（考虑NaN）
    def arrays_equal_with_nan(a, b):
        """比较两个可能包含NaN的数组"""
        if a.shape != b.shape:
            return False
        
        # 处理NaN位置
        nan_mask_a = np.isnan(a)
        nan_mask_b = np.isnan(b)
        
        # NaN位置必须一致
        if not np.array_equal(nan_mask_a, nan_mask_b):
            return False
        
        # 非NaN位置的值必须一致
        non_nan_mask = ~nan_mask_a
        return np.allclose(a[non_nan_mask], b[non_nan_mask])
    
    data_match = arrays_equal_with_nan(fenlei_encoded_data, dlvs_encoded_data)
    print(f"编码数据一致: {data_match}")
    
    # 检查列位置是否一致
    locations_match = column_locations == dlvs_column_locations
    print(f"列位置一致: {locations_match}")
    
    if shape_match and data_match and locations_match:
        print("\n✅ 两个实现完全一致！")
        return True
    else:
        print("\n❌ 两个实现存在差异！")
        if not shape_match:
            print(f"  形状不一致: fenlei={fenlei_encoded_data.shape} vs dlvs={dlvs_encoded_data.shape}")
        if not data_match:
            print("  数据不一致")
            diff_mask = ~arrays_equal_with_nan(fenlei_encoded_data, dlvs_encoded_data)
            print(f"  差异位置数量: {np.sum(diff_mask) if hasattr(diff_mask, 'sum') else 'N/A'}")
        if not locations_match:
            print(f"  列位置不一致: fenlei={column_locations} vs dlvs={dlvs_column_locations}")
        return False

def test_loss_function_comparison():
    """对比损失函数实现"""
    print("\n=== 损失函数实现对比 ===")
    
    print("fenlei项目损失函数特点:")
    print("- 连续变量: MSE损失 (M*X - M*G_sample)^2")
    print("- 分类变量: 交叉熵损失 -X*M*log(G_sample + 1e-8)")
    print("- 按变量类型分别计算，最后归一化")
    
    print("\nDL-vs-Stat项目损失函数特点:")
    print("- 连续变量: MSE损失 (gen_x*m - x*m)^2")
    print("- 分类变量: 交叉熵损失 -x*m*log(gen_x + 1e-8)")
    print("- 按列位置遍历，根据label_reverse判断类型")
    
    print("\n✅ 损失函数计算逻辑一致，只是实现方式略有不同")

if __name__ == "__main__":
    print("检查fenlei项目是否完全按照DL-vs-Stat_Impute-main实现了One-Hot编码\n")
    
    # 测试One-Hot编码
    encoding_consistent = test_onehot_encoding_comparison()
    
    # 测试损失函数
    test_loss_function_comparison()
    
    print(f"\n{'='*50}")
    if encoding_consistent:
        print("🎉 总结: fenlei项目的One-Hot编码实现与DL-vs-Stat_Impute-main完全一致！")
    else:
        print("⚠️  总结: 存在一些差异，需要进一步调整")
