# main_letter_spam.py
# coding=utf-8
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
import argparse
import numpy as np
import tensorflow.compat.v1 as tf

import os
import pandas as pd
tf.disable_v2_behavior()

# 根据你自己的项目结构，确保以下导入路径正确
from data_loader import data_loader
from gaing import gain 
#gaing~~~rawgain~~~sgtgain
from utils import rmse_loss
import time

def single_run(data_name, mr, seed, batch_size, hint_rate, alpha, iterations):
    """
    针对单个缺失率 mr 执行一次完整的 GAIN 训练和插补流程:
      1. tf.reset_default_graph()
      2. 设置随机种子
      3. 读取数据并制造缺失
      4. 训练 GAIN
      5. 计算并返回 RMSE
    这样就和“单脚本只跑一次 miss_rate”的行为最接近。
    """
    start_time = time.time()
    # 1) 重置默认图, 保证每次都是全新图
    tf.reset_default_graph()

    # 2) 设置随机种子
    tf.set_random_seed(seed)
    np.random.seed(seed)

    # 3) 读数据
    ori_data_x, miss_data_x, data_m = data_loader(data_name, mr)

    # 4) 定义 GAIN 参数
    gain_parameters = {
        'batch_size': batch_size,
        'hint_rate': hint_rate,
        'alpha': alpha,
        'iterations': iterations
    }

    # 5) 训练 GAIN 并得到插补结果
    imputed_data_x = gain(miss_data_x, gain_parameters)

    # 保存插补后的数据
    # save_dir = f'imputed_data_{data_name}'
    # if not os.path.exists(save_dir):
    #     os.makedirs(save_dir)
    #
    # save_path = os.path.join(save_dir, f'imputed_rate_{mr:.1f}.csv')
    # pd.DataFrame(imputed_data_x).to_csv(save_path, index=False)

    # 6) 计算 RMSE
    rmse_val = rmse_loss(ori_data_x, imputed_data_x, data_m)
    run_time = time.time() - start_time
    return rmse_val, run_time


def main(args):
    """
    在这里，我们一次性测试多个缺失率(0.1, 0.2, 0.3, 0.4, 0.5)，
    每个缺失率都单独调用 single_run(...)，保证训练互不干扰。
    """
    # rates = [0.1, 0.2, 0.3, 0.4, 0.5]
    rates = [0.2]
    results = []
    times = []

    for mr in rates:
        print("======================================")
        print(f"Running single run with missing_rate = {mr}")

        # 调用 single_run，每次都相当于“单脚本只跑一次”
        rmse_val, run_time = single_run(
            data_name=args.data_name,
            mr=mr,
            seed=args.seed,
            batch_size=args.batch_size,
            hint_rate=args.hint_rate,
            alpha=args.alpha,
            iterations=args.iterations
        )

        print(f"Missing rate {mr} => RMSE: {rmse_val:.4f}, Time: {run_time:.2f}s")
        results.append((mr, rmse_val))
        times.append(run_time)

    # 总结输出
    print(f"=====================================================")
    # 动态获取算法名称
    algorithm_name = gain.__module__  # 获取导入的模块名
    print(f"{algorithm_name}--  {args.data_name}")
    print(f"Size: {args.batch_size}-- H: {args.hint_rate}-- A: {args.alpha}-- Ite: {args.iterations}\n--------------------")
    print("Missing | RMSE   | Time(s)")
    #print("------------ | ------- | -------")
    for (mr, rm), t in zip(results, times):
        print(f"{mr:11.1f} | {rm:7.4f} | {t:7.2f}")
    
    print(f"=====================================================")

    return results, times


if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    parser.add_argument(
        '--seed',
        help='随机种子，用于确保结果可重复',
        default=66,
        type=int
    )
    parser.add_argument(
        '--data_name',
        choices=['breast','spam','letter'],
        default='breast',
        type=str
    )
    parser.add_argument(
        '--batch_size',
        help='mini-batch 大小',
        default=64,
        type=int
    )
    parser.add_argument(
        '--hint_rate',
        help='hint 概率',
        default=0.9,
        type=float
    )
    parser.add_argument(
        '--alpha',
        help='超参数',
        default=1000,
        type=float
    )
    parser.add_argument(
        '--iterations',
        help='训练迭代次数',
        default=10000,
        type=int
    )

    args = parser.parse_args()

    # 运行主函数
    main(args)
