# coding=utf-8
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

'''GAIN function with One-Hot encoding for categorical variables.
Date: 2020/02/28
Reference: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "GAIN: Missing Data
           Imputation using Generative Adversarial Nets," ICML, 2018.
Paper Link: http://proceedings.mlr.press/v80/yoon18a/yoon18a.pdf
Contact: <EMAIL>

Modified to handle categorical variables with One-Hot encoding.
Categorical variables are identified as features with unique values < 20.
'''

# Necessary packages
# import tensorflow as tf
##IF USING TF 2 use following import to still use TF < 2.0 Functionalities
import tensorflow.compat.v1 as tf

tf.disable_v2_behavior()

import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import os

from utils import xavier_init
from utils import binary_sampler, uniform_sampler, sample_batch_index


def identify_variable_types(data_x, categorical_threshold=20):
    '''Identify categorical and continuous variables.

    Args:
      - data_x: original data with missing values
      - categorical_threshold: threshold for identifying categorical variables

    Returns:
      - variable_types: list of 'cat' or 'con' for each variable
      - categorical_info: dictionary containing categorical variable information
    '''
    no, dim = data_x.shape
    variable_types = []
    categorical_info = {}

    for i in range(dim):
        # Get non-missing values for this column
        non_missing_values = data_x[~np.isnan(data_x[:, i]), i]
        unique_values = np.unique(non_missing_values)

        if len(unique_values) < categorical_threshold:
            variable_types.append('cat')
            # Store mapping for categorical variables
            categorical_info[i] = {
                'unique_values': unique_values,
                'value_to_index': {val: idx for idx, val in enumerate(unique_values)},
                'index_to_value': {idx: val for idx, val in enumerate(unique_values)}
            }
        else:
            variable_types.append('con')

    return variable_types, categorical_info


def encode_categorical_variables(data_x, variable_types, categorical_info):
    '''Encode categorical variables using One-Hot encoding.

    Args:
      - data_x: original data with missing values
      - variable_types: list of 'cat' or 'con' for each variable
      - categorical_info: dictionary containing categorical variable information

    Returns:
      - encoded_data: data with categorical variables one-hot encoded
      - column_locations: end positions of each original variable in encoded data
      - encoding_info: information needed for decoding
    '''
    no, dim = data_x.shape
    encoded_columns = []
    column_locations = []
    encoding_info = []
    current_pos = 0

    for i in range(dim):
        if variable_types[i] == 'con':
            # Continuous variable: keep as is
            encoded_columns.append(data_x[:, i:i+1])
            current_pos += 1
            column_locations.append(current_pos)
            encoding_info.append({'type': 'con'})
        else:
            # Categorical variable: one-hot encode
            cat_info = categorical_info[i]
            unique_values = cat_info['unique_values']
            n_categories = len(unique_values)

            # Create one-hot encoded matrix
            one_hot_matrix = np.full((no, n_categories), np.nan)

            for row_idx in range(no):
                if not np.isnan(data_x[row_idx, i]):
                    value = data_x[row_idx, i]
                    if value in cat_info['value_to_index']:
                        cat_idx = cat_info['value_to_index'][value]
                        one_hot_matrix[row_idx, :] = 0
                        one_hot_matrix[row_idx, cat_idx] = 1

            encoded_columns.append(one_hot_matrix)
            current_pos += n_categories
            column_locations.append(current_pos)
            encoding_info.append({
                'type': 'cat',
                'n_categories': n_categories,
                'categorical_info': cat_info
            })

    # Concatenate all encoded columns
    encoded_data = np.concatenate(encoded_columns, axis=1)

    return encoded_data, column_locations, encoding_info


def normalization_mixed(data, variable_types, column_locations, encoding_info, parameters=None):
    '''Normalize mixed data with categorical and continuous variables.

    Args:
      - data: encoded data with missing values
      - variable_types: list of 'cat' or 'con' for each original variable
      - column_locations: end positions of each original variable in encoded data
      - encoding_info: information about encoding
      - parameters: normalization parameters (for renormalization)

    Returns:
      - norm_data: normalized data
      - norm_parameters: normalization parameters
    '''
    no, dim = data.shape
    norm_data = data.copy()

    if parameters is None:
        min_val = np.zeros(dim)
        max_val = np.zeros(dim)

        start_pos = 0
        for i, end_pos in enumerate(column_locations):
            if encoding_info[i]['type'] == 'con':
                # Normalize continuous variables
                col_data = norm_data[:, start_pos:end_pos]
                min_val[start_pos:end_pos] = np.nanmin(col_data, axis=0)
                norm_data[:, start_pos:end_pos] = col_data - min_val[start_pos:end_pos]
                max_val[start_pos:end_pos] = np.nanmax(norm_data[:, start_pos:end_pos], axis=0)
                norm_data[:, start_pos:end_pos] = norm_data[:, start_pos:end_pos] / (max_val[start_pos:end_pos] + 1e-6)
            else:
                # Categorical variables: keep as is (already 0/1)
                min_val[start_pos:end_pos] = 0
                max_val[start_pos:end_pos] = 1

            start_pos = end_pos

        norm_parameters = {
            'min_val': min_val,
            'max_val': max_val,
            'variable_types': variable_types,
            'column_locations': column_locations,
            'encoding_info': encoding_info
        }
    else:
        min_val = parameters['min_val']
        max_val = parameters['max_val']

        start_pos = 0
        for i, end_pos in enumerate(column_locations):
            if encoding_info[i]['type'] == 'con':
                # Normalize continuous variables
                norm_data[:, start_pos:end_pos] = norm_data[:, start_pos:end_pos] - min_val[start_pos:end_pos]
                norm_data[:, start_pos:end_pos] = norm_data[:, start_pos:end_pos] / (max_val[start_pos:end_pos] + 1e-6)

            start_pos = end_pos

        norm_parameters = parameters

    return norm_data, norm_parameters


def renormalization_mixed(norm_data, norm_parameters):
    '''Renormalize mixed data from [0, 1] range to the original range.

    Args:
      - norm_data: normalized data
      - norm_parameters: normalization parameters

    Returns:
      - renorm_data: renormalized data
    '''
    min_val = norm_parameters['min_val']
    max_val = norm_parameters['max_val']
    column_locations = norm_parameters['column_locations']
    encoding_info = norm_parameters['encoding_info']

    no, dim = norm_data.shape
    renorm_data = norm_data.copy()

    start_pos = 0
    for i, end_pos in enumerate(column_locations):
        if encoding_info[i]['type'] == 'con':
            # Renormalize continuous variables
            renorm_data[:, start_pos:end_pos] = renorm_data[:, start_pos:end_pos] * (max_val[start_pos:end_pos] + 1e-6)
            renorm_data[:, start_pos:end_pos] = renorm_data[:, start_pos:end_pos] + min_val[start_pos:end_pos]
        # Categorical variables remain as is

        start_pos = end_pos

    return renorm_data


def decode_categorical_variables(encoded_data, variable_types, column_locations, encoding_info, original_shape):
    '''Decode one-hot encoded categorical variables back to original format.

    Args:
      - encoded_data: data with one-hot encoded categorical variables
      - variable_types: list of 'cat' or 'con' for each original variable
      - column_locations: end positions of each original variable in encoded data
      - encoding_info: information about encoding
      - original_shape: shape of original data

    Returns:
      - decoded_data: data in original format
    '''
    no, original_dim = original_shape
    decoded_data = np.zeros((no, original_dim))

    start_pos = 0
    original_col = 0

    for i, end_pos in enumerate(column_locations):
        if encoding_info[i]['type'] == 'con':
            # Continuous variable: copy directly
            decoded_data[:, original_col] = encoded_data[:, start_pos]
        else:
            # Categorical variable: decode from one-hot
            cat_info = encoding_info[i]['categorical_info']
            one_hot_data = encoded_data[:, start_pos:end_pos]

            for row_idx in range(no):
                # Find the category with highest probability
                max_idx = np.argmax(one_hot_data[row_idx, :])
                if max_idx in cat_info['index_to_value']:
                    decoded_data[row_idx, original_col] = cat_info['index_to_value'][max_idx]
                else:
                    # If no clear maximum, use the most frequent category
                    decoded_data[row_idx, original_col] = cat_info['unique_values'][0]

        start_pos = end_pos
        original_col += 1

    return decoded_data


def rounding_mixed(imputed_data, original_data, variable_types):
    '''Round imputed data for categorical variables.

    Args:
      - imputed_data: imputed data
      - original_data: original data with missing values
      - variable_types: list of 'cat' or 'con' for each variable

    Returns:
      - rounded_data: rounded imputed data
    '''
    no, dim = imputed_data.shape
    rounded_data = imputed_data.copy()

    for i in range(dim):
        if variable_types[i] == 'cat':
            # Round categorical variables
            rounded_data[:, i] = np.round(rounded_data[:, i])

    return rounded_data


def gain(data_x, gain_parameters):
    '''Impute missing values in data_x with One-Hot encoding for categorical variables

    Args:
      - data_x: original data with missing values
      - gain_parameters: GAIN network parameters:
        - batch_size: Batch size
        - hint_rate: Hint rate
        - alpha: Hyperparameter
        - iterations: Iterations

    Returns:
      - imputed_data: imputed data
    '''
    # Step 1: Identify variable types
    variable_types, categorical_info = identify_variable_types(data_x)

    # Step 2: Encode categorical variables
    encoded_data, column_locations, encoding_info = encode_categorical_variables(data_x, variable_types, categorical_info)

    # Step 3: Define mask matrix for encoded data
    data_m = 1 - np.isnan(encoded_data)

    # System parameters
    batch_size = gain_parameters['batch_size']
    hint_rate = gain_parameters['hint_rate']
    alpha = gain_parameters['alpha']
    iterations = gain_parameters['iterations']

    # Other parameters
    no, encoded_dim = encoded_data.shape

    # Hidden state dimensions
    h_dim = int(encoded_dim)

    # Normalization for mixed data
    norm_data, norm_parameters = normalization_mixed(encoded_data, variable_types, column_locations, encoding_info)
    norm_data_x = np.nan_to_num(norm_data, 0)

    ## GAIN architecture
    # Input placeholders
    # Data vector
    X = tf.placeholder(tf.float32, shape=[None, encoded_dim])
    # Mask vector
    M = tf.placeholder(tf.float32, shape=[None, encoded_dim])
    # Hint vector
    H = tf.placeholder(tf.float32, shape=[None, encoded_dim])

    # Discriminator variables
    D_W1 = tf.Variable(xavier_init([encoded_dim * 2, h_dim]))  # Data + Hint as inputs
    D_b1 = tf.Variable(tf.zeros(shape=[h_dim]))

    D_W2 = tf.Variable(xavier_init([h_dim, h_dim]))
    D_b2 = tf.Variable(tf.zeros(shape=[h_dim]))

    D_W3 = tf.Variable(xavier_init([h_dim, encoded_dim]))
    D_b3 = tf.Variable(tf.zeros(shape=[encoded_dim]))  # Multi-variate outputs

    theta_D = [D_W1, D_W2, D_W3, D_b1, D_b2, D_b3]

    # Generator variables
    # Data + Mask as inputs (Random noise is in missing components)
    G_W1 = tf.Variable(xavier_init([encoded_dim * 2, h_dim]))
    G_b1 = tf.Variable(tf.zeros(shape=[h_dim]))

    G_W2 = tf.Variable(xavier_init([h_dim, h_dim]))
    G_b2 = tf.Variable(tf.zeros(shape=[h_dim]))

    G_W3 = tf.Variable(xavier_init([h_dim, encoded_dim]))
    G_b3 = tf.Variable(tf.zeros(shape=[encoded_dim]))

    theta_G = [G_W1, G_W2, G_W3, G_b1, G_b2, G_b3]

    ## GAIN functions
    # Generator
    def generator(x, m):
        # Concatenate Mask and Data
        inputs = tf.concat(values=[x, m], axis=1)
        G_h1 = tf.nn.relu(tf.matmul(inputs, G_W1) + G_b1)
        G_h2 = tf.nn.relu(tf.matmul(G_h1, G_W2) + G_b2)
        # MinMax normalized output
        G_prob = tf.nn.sigmoid(tf.matmul(G_h2, G_W3) + G_b3)
        return G_prob

    # Discriminator
    def discriminator(x, h):
        # Concatenate Data and Hint
        inputs = tf.concat(values=[x, h], axis=1)
        D_h1 = tf.nn.relu(tf.matmul(inputs, D_W1) + D_b1)
        D_h2 = tf.nn.relu(tf.matmul(D_h1, D_W2) + D_b2)
        D_logit = tf.matmul(D_h2, D_W3) + D_b3
        D_prob = tf.nn.sigmoid(D_logit)
        return D_prob

    ## GAIN structure
    # Generator
    G_sample = generator(X, M)

    # Combine with observed data
    Hat_X = X * M + G_sample * (1 - M)

    # Discriminator
    D_prob = discriminator(Hat_X, H)

    ## GAIN loss with mixed variable types
    D_loss_temp = -tf.reduce_mean(M * tf.log(D_prob + 1e-8) \
                                  + (1 - M) * tf.log(1. - D_prob + 1e-8))

    G_loss_temp = -tf.reduce_mean((1 - M) * tf.log(D_prob + 1e-8))

    # Mixed loss for different variable types
    mixed_loss = tf.constant(0.0)
    loss_balance = 1.0

    start_pos = 0
    for i, end_pos in enumerate(column_locations):
        if encoding_info[i]['type'] == 'con':
            # MSE loss for continuous variables
            con_loss = tf.reduce_sum((M[:, start_pos:end_pos] * X[:, start_pos:end_pos] -
                                    M[:, start_pos:end_pos] * G_sample[:, start_pos:end_pos]) ** 2)
            mixed_loss = mixed_loss + con_loss
        else:
            # Cross-entropy loss for categorical variables (one-hot encoded)
            cat_loss_target = -X[:, start_pos:end_pos] * M[:, start_pos:end_pos] * tf.log(G_sample[:, start_pos:end_pos] + 1e-8)
            cat_loss = loss_balance * tf.reduce_sum(cat_loss_target)
            mixed_loss = mixed_loss + cat_loss

        start_pos = end_pos

    # Normalize by number of observed values
    mixed_loss = mixed_loss / (tf.reduce_sum(M) + 1e-8)

    D_loss = D_loss_temp
    G_loss = G_loss_temp + alpha * mixed_loss

    ## GAIN solver
    D_solver = tf.train.AdamOptimizer().minimize(D_loss, var_list=theta_D)
    G_solver = tf.train.AdamOptimizer().minimize(G_loss, var_list=theta_G)

    ## Iterations
    sess = tf.Session()
    sess.run(tf.global_variables_initializer())

    # Lists to store loss values for plotting
    D_losses = []
    G_losses = []
    iterations_list = []

    # Start Iterations
    for it in tqdm(range(iterations)):
        # Sample batch
        batch_idx = sample_batch_index(no, batch_size)
        X_mb = norm_data_x[batch_idx, :]
        M_mb = data_m[batch_idx, :]
        # Sample random vectors
        Z_mb = uniform_sampler(0, 0.01, batch_size, encoded_dim)
        # Sample hint vectors
        H_mb_temp = binary_sampler(hint_rate, batch_size, encoded_dim)
        H_mb = M_mb * H_mb_temp

        # Combine random vectors with observed vectors
        X_mb = M_mb * X_mb + (1 - M_mb) * Z_mb

        _, D_loss_curr = sess.run([D_solver, D_loss_temp],
                                  feed_dict={M: M_mb, X: X_mb, H: H_mb})
        _, G_loss_curr, mixed_loss_curr = \
            sess.run([G_solver, G_loss_temp, mixed_loss],
                     feed_dict={X: X_mb, M: M_mb, H: H_mb})

        # Record losses every 100 iterations to avoid too much data
        if it % 100 == 0:
            D_losses.append(D_loss_curr)
            G_losses.append(G_loss_curr)
            iterations_list.append(it)

    ## Return imputed data
    Z_mb = uniform_sampler(0, 0.01, no, encoded_dim)
    M_mb = data_m
    X_mb = norm_data_x
    X_mb = M_mb * X_mb + (1 - M_mb) * Z_mb

    imputed_encoded_data = sess.run([G_sample], feed_dict={X: X_mb, M: M_mb})[0]

    imputed_encoded_data = data_m * norm_data_x + (1 - data_m) * imputed_encoded_data

    # Renormalization for encoded data
    imputed_encoded_data = renormalization_mixed(imputed_encoded_data, norm_parameters)

    # Decode back to original format
    imputed_data = decode_categorical_variables(imputed_encoded_data, variable_types, column_locations, encoding_info, data_x.shape)

    # Round categorical variables
    imputed_data = rounding_mixed(imputed_data, data_x, variable_types)

    # Return imputed data
    return imputed_data
