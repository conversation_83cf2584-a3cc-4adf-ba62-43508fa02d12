# 使用真实数据测试分类变量识别
import numpy as np
import pandas as pd
import os

def test_with_real_data():
    """使用真实数据集测试变量识别和编码"""
    print("=== 使用真实数据测试变量识别 ===\n")
    
    # 读取credit数据集
    script_dir = os.path.dirname(os.path.abspath(__file__))
    data_path = os.path.join(script_dir, 'data', 'credit.csv')
    
    if not os.path.exists(data_path):
        print(f"数据文件不存在: {data_path}")
        return
    
    df = pd.read_csv(data_path)
    print(f"Credit数据集形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 转换为numpy数组
    data_x = df.values.astype(float)
    
    # 分析每列的唯一值数量
    print("\n各列唯一值分析:")
    print("列索引 | 唯一值数量 | 识别类型 | 前几个唯一值")
    print("-" * 60)
    
    variable_types = []
    categorical_info = {}
    categorical_threshold = 20
    
    for i in range(data_x.shape[1]):
        non_missing_values = data_x[~np.isnan(data_x[:, i]), i]
        unique_values = np.unique(non_missing_values)
        n_unique = len(unique_values)
        
        if n_unique < categorical_threshold:
            var_type = 'cat'
            variable_types.append('cat')
            categorical_info[i] = {
                'unique_values': unique_values,
                'value_to_index': {val: idx for idx, val in enumerate(unique_values)},
                'index_to_value': {idx: val for idx, val in enumerate(unique_values)}
            }
        else:
            var_type = 'con'
            variable_types.append('con')
        
        # 显示前几个唯一值
        sample_values = unique_values[:5] if len(unique_values) > 5 else unique_values
        sample_str = ', '.join([f"{v:.2f}" if v == int(v) else f"{v:.3f}" for v in sample_values])
        if len(unique_values) > 5:
            sample_str += "..."
        
        print(f"{i:6d} | {n_unique:10d} | {var_type:8s} | {sample_str}")
    
    print(f"\n总结:")
    print(f"连续变量数量: {variable_types.count('con')}")
    print(f"分类变量数量: {variable_types.count('cat')}")
    
    # 测试编码过程
    if variable_types.count('cat') > 0:
        print(f"\n=== 测试One-Hot编码 ===")
        
        # 取前1000行进行测试
        test_data = data_x[:1000, :]
        
        # 执行编码
        encoded_columns = []
        column_locations = []
        encoding_info = []
        current_pos = 0
        
        for i in range(test_data.shape[1]):
            if variable_types[i] == 'con':
                # 连续变量: 保持不变
                encoded_columns.append(test_data[:, i:i+1])
                current_pos += 1
                column_locations.append(current_pos)
                encoding_info.append({'type': 'con'})
            else:
                # 分类变量: one-hot编码
                cat_info = categorical_info[i]
                unique_values = cat_info['unique_values']
                n_categories = len(unique_values)
                
                # 创建one-hot编码矩阵
                one_hot_matrix = np.full((1000, n_categories), np.nan)
                
                for row_idx in range(1000):
                    if not np.isnan(test_data[row_idx, i]):
                        value = test_data[row_idx, i]
                        if value in cat_info['value_to_index']:
                            cat_idx = cat_info['value_to_index'][value]
                            one_hot_matrix[row_idx, :] = 0
                            one_hot_matrix[row_idx, cat_idx] = 1
                
                encoded_columns.append(one_hot_matrix)
                current_pos += n_categories
                column_locations.append(current_pos)
                encoding_info.append({
                    'type': 'cat',
                    'n_categories': n_categories,
                    'categorical_info': cat_info
                })
        
        # 拼接所有编码列
        encoded_data = np.concatenate(encoded_columns, axis=1)
        
        print(f"原始数据形状: {test_data.shape}")
        print(f"编码后数据形状: {encoded_data.shape}")
        print(f"维度扩展: {encoded_data.shape[1] - test_data.shape[1]} 列")
        print(f"列位置: {column_locations}")
        
        # 检查编码结果
        print(f"\n编码质量检查:")
        total_missing_original = np.sum(np.isnan(test_data))
        total_missing_encoded = np.sum(np.isnan(encoded_data))
        print(f"原始数据缺失值: {total_missing_original}")
        print(f"编码后缺失值: {total_missing_encoded}")
        
        # 检查分类变量的one-hot编码是否正确
        start_pos = 0
        for i, end_pos in enumerate(column_locations):
            if encoding_info[i]['type'] == 'cat':
                cat_data = encoded_data[:, start_pos:end_pos]
                # 检查每行是否只有一个1（对于非缺失值）
                row_sums = np.nansum(cat_data, axis=1)
                valid_rows = ~np.isnan(row_sums)
                correct_encoding = np.all(row_sums[valid_rows] == 1.0)
                print(f"变量{i} (分类): One-Hot编码正确 = {correct_encoding}")
            
            start_pos = end_pos
        
        print(f"\n✅ One-Hot编码测试完成")
    else:
        print(f"\n该数据集没有分类变量（唯一值<{categorical_threshold}）")

if __name__ == "__main__":
    test_with_real_data()
